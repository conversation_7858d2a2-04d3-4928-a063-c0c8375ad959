@use '../../styles/CommonStyles.scss';

.reportCashSalesContainer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  height: 100%;
}

.reportCashSalesContent {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: var(--u-space-three);
  flex: 1;

  @media only screen and (max-width: CommonStyles.$max-mobile-width) {
    padding: var(--u-space-two);
  }
}

.headerContainer {
  width: 100%;
  display: flex;
  gap: var(--u-space-one);
  padding-bottom: var(--u-space-one);
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}

.headerNameContainer {
  display: flex;
  gap: var(--u-space-one);
  align-items: center;
  max-width: 70%;

  @media only screen and (max-width: CommonStyles.$max-mobile-width) {
    max-width: 100%;
  }
}

.headerTitle {
  overflow-wrap: break-word;
}

.errorAndLoadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  flex-direction: column;
}

.errorNote {
  margin-bottom: var(--u-space-one);
  text-align: center;
}

.helperText {
  margin-bottom: var(--u-space-one);
}

.ticketTypeInputsContainer {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-one);
  padding-bottom: 10em;
}

.reportCashSalesFooter {
  display: flex;
  justify-content: right;
  width: 100%;
  padding: var(--u-space-three);
  padding-top: 0;
  gap: var(--u-space-one);
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: var(--u-color-grey-100);
  z-index: 1000;

  @media only screen and (max-width: CommonStyles.$max-mobile-width) {
    padding: var(--u-space-two);
  }
}

.footerContainer {
  display: flex;
  flex-direction: column;
  column-gap: var(--u-space-half);
  border-top: var(--space-eighth) solid var(--color-bg-level0-accent);
  width: 100%;
  gap: var(--u-space-one);
}

.totalSalesText {
  margin-top: var(--u-space-one);
  text-align: right;
  font-weight: var(--u-font-weight-bold);
}

.reportCashSalesButtonContainer {
  display: flex;
  gap: var(--u-space-half);
  justify-content: right;
}
