import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { formDescription } from '../../hooks/FormShared/FormDescription/useFormDescription';
import { modified } from '../../hooks/FormShared/FormModified/useFormModified';
import { formTitle } from '../../hooks/FormShared/FormTitle/useFormTitle';
import { renderWithOptions } from '../../test/renderHelpers';
import TicketedEventDetailsForm from './TicketedEventDetailsForm';

vi.mock('react-router-dom', () => ({
  useSearchParams: vi.fn(() => {
    return [new URLSearchParams({})];
  }),
}));

beforeEach(() => {
  formTitle('');
  formDescription('');
  modified(false);

  vi.mock('react-router-dom', () => ({
    useSearchParams: vi.fn(() => {
      return [new URLSearchParams({})];
    }),
  }));
});

describe('TicketedEventDetailsForm Tests', () => {
  it('Renders TicketedEventDetailsForm', () => {
    formTitle('Testing Event Title');
    renderWithOptions(<TicketedEventDetailsForm />, {
      withIntlProvider: true,
    });

    expect(screen.getByTestId('event-title-input')).toHaveValue('Testing Event Title');
    expect(screen.getByTestId('event-description-input')).toHaveValue('');
  });

  it('Error state when title is empty string', async () => {
    const user = userEvent.setup({ delay: null });
    renderWithOptions(<TicketedEventDetailsForm />, {
      withIntlProvider: true,
    });

    await user.click(screen.getByTestId('event-title-input'));
    await user.click(screen.getByTestId('event-description-input'));

    expect(screen.getByTestId('event-title-input-error')).toBeInTheDocument();
    expect(screen.getByText('Event title is required.')).toBeInTheDocument();
  });

  it('Error state when title is too long', () => {
    formTitle('T'.repeat(200));
    renderWithOptions(<TicketedEventDetailsForm />, {
      withIntlProvider: true,
    });

    expect(screen.getByTestId('event-title-input-error')).toBeInTheDocument();
    expect(screen.getByText('Character limit: 200/150')).toBeInTheDocument();
  });

  it('Error state when description is too long', () => {
    formTitle('Testing Event Title');
    formDescription('T'.repeat(2002));
    renderWithOptions(<TicketedEventDetailsForm />, {
      withIntlProvider: true,
    });

    expect(screen.getByTestId('event-description-input-error')).toBeInTheDocument();
    expect(screen.getByText('Character limit: 2002/2000')).toBeInTheDocument();
  });

  it('isTicketedEventModified true when description changes', async () => {
    const user = userEvent.setup({ delay: null });
    formTitle('Testing Event Title');
    renderWithOptions(<TicketedEventDetailsForm />, {
      withIntlProvider: true,
    });

    await user.clear(screen.getByTestId('event-description-input'));
    await user.type(screen.getByTestId('event-description-input'), 'Testing Event Description');
    expect(modified).toBeTruthy();
    expect(screen.getByTestId('event-description-input')).toHaveValue('Testing Event Description');
  });

  it('isTicketedEventModified true when title changes', async () => {
    const user = userEvent.setup({ delay: null });
    formTitle('Testing Event Title');
    renderWithOptions(<TicketedEventDetailsForm />, {
      withIntlProvider: true,
    });

    await user.clear(screen.getByTestId('event-title-input'));
    await user.type(screen.getByTestId('event-title-input'), 'Testing Event Title Changes');
    expect(modified).toBeTruthy();
    expect(screen.getByTestId('event-title-input')).toHaveValue('Testing Event Title Changes');
  });
});
