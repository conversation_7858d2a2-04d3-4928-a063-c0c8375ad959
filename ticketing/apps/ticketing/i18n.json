{"version": 1.1, "pre-load-external-sets": ["seating-charts", "ticketing-shared"], "sets": {"ticketing": {"keys": ["ticketing.home-page.hudl-ticketing", "ticketing.home-page.ticketing-menu", "ticketing.home-page.create-button", "ticketing.home-page.create-pass", "ticketing.home-page.no-scheduled-events", "ticketing.home-page.schedule-upcoming-events", "ticketing.home-page.no-published-events", "ticketing.home-page.no-draft-events", "ticketing.home-page.no-past-events", "ticketing.home-page.publish-ticketed-event", "ticketing.home-page.draft-ticketed-event", "ticketing.home-page.past-ticketed-event", "ticketing.home-page.draft-pass", "ticketing.home-page.upcoming-events", "ticketing.home-page.non-ticketed-events", "ticketing.home-page.active-passes", "ticketing.home-page.current-passes", "ticketing.home-page.draft-passes", "ticketing.home-page.past-passes", "ticketing.home-page.no-past-passes", "ticketing.home-page.past-passes-text", "ticketing.home-page.draft-events", "ticketing.home-page.past-events", "ticketing.home-page.coming-soon", "ticketing.home-page.coming-soon-passes", "ticketing.home-page.past-events-unavailable", "ticketing.home-page.error-finding-school", "ticketing.home-page.error-finding-schedule-entries", "ticketing.home-page.error-finding-ticketed-events", "ticketing.home-page.error-fetching-account-status", "ticketing.home-page.error-finding-passes", "ticketing.home-page.error-non-us-school", "ticketing.home-page.load-more", "ticketing.home-page.your-ticketed-event", "ticketing.home-page.your-pass", "ticketing.home-page.share-pass", "ticketing.home-page.share-pass.instructions", "ticketing.home-page.share-event", "ticketing.home-page.share-event.instructions", "ticketing.home-page.share-event.copy-link", "ticketing.home-page.share-event.link-copied", "ticketing.home-page.share-event.link-copy-failed", "ticketing.home-page.payouts-analytics-action", "ticketing.home-page.scanner-access-code-action", "ticketing.home-page.event-action-dropdown.share", "ticketing.home-page.event-action-dropdown.edit", "ticketing.home-page.event-action-dropdown.reportCashSales", "ticketing.home-page.no-passes", "ticketing.home-page.no-draft-passes", "ticketing.home-page.publish-passes-empty", "ticketing.home-page.passes-header-pass", "ticketing.home-page.passes-header-active-time-period", "ticketing.home-page.passes-header-passes-sold", "ticketing.home-page.passes-header-teams", "ticketing.home-page.submit-schedules", "ticketing.tooltip.no-backdoored-users", "ticketing.onboarding.header-title", "ticketing.onboarding.header-subtitle", "ticketing.onboarding.finish-button", "ticketing.onboarding.stripe-account.title", "ticketing.onboarding.stripe-account.description", "ticketing.onboarding.stripe-account.button", "ticketing.onboarding.terms-of-service.title", "ticketing.onboarding.terms-of-service.description", "ticketing.onboarding.terms-of-service.button", "ticketing.onboarding.tos-modal.decline-button", "ticketing.onboarding.tos-modal.header", "ticketing.onboarding.tos-modal.subhead", "ticketing.onboarding.tos-modal.agree-button", "ticketing.onboarding.tos-modal.signature-name", "ticketing.onboarding.tos-modal.error.loading-agreement", "ticketing.onboarding.tos-modal.error.submitting-agreement", "ticketing.onboarding.ticketing-settings.page-title", "ticketing.onboarding.ticketing-settings.page-subtitle", "ticketing.onboarding.ticketing-settings.timezone-section-title", "ticketing.onboarding.ticketing-settings.cancel-button", "ticketing.onboarding.ticketing-settings.save-button", "ticketing.onboarding.admin-request.page-title", "ticketing.onboarding.admin-request.page-subtitle", "ticketing.onboarding.admin-request.user-information-section-title", "ticketing.onboarding.admin-request.add-another", "ticketing.onboarding.admin-request.error-saving", "ticketing.onboarding.admin-request.users-added-section-title", "ticketing.home-page.tickets-sold", "ticketing.add-ticketing-page.add-ticketing", "ticketing.add-ticketing-page.review-ticketing", "ticketing.add-ticketing-page.update-ticketing", "ticketing.add-ticketing-page.scheduled-event", "ticketing.add-ticketing-page.event-details-instructions", "ticketing.add-ticketing-page.event-title", "ticketing.add-ticketing-page.event-title-error-help-text", "ticketing.add-ticketing-page.event-description", "ticketing.add-ticketing-page.event-information", "ticketing.add-ticketing-page.ticketed-event-details", "ticketing.add-ticketing-page.review-event-information", "ticketing.add-ticketing-page.event-description-placeholder", "ticketing.add-ticketing-page.disclaimer", "ticketing.add-ticketing-page.ticket-pricing", "ticketing.add-ticketing-page.ticket-pricing-description", "ticketing.add-ticketing-page.ticket-pricing-description-required-fields", "ticketing.add-ticketing-page.quantity-for-sale", "ticketing.add-ticketing-page.quantity-error-help-text", "ticketing.add-ticketing-page.quantity-help-text", "ticketing.add-ticketing-page.quantity-placeholder", "ticketing.add-ticketing-page.type.hardcoded-free-admission", "ticketing.add-ticketing-page.error-publishing-ticketed-event", "ticketing.add-ticketing-page.success-publishing-ticketed-event", "ticketing.add-ticketing-page.success-saving-draft-ticketed-event", "ticketing.add-ticketing-page.error-finding-ticketed-event", "ticketing.add-ticketing-page.event-title-character-limit", "ticketing.add-ticketing-page.event-description-help-text", "ticketing.add-ticketing-page.select-ticket-price-option", "ticketing.add-ticketing-page.add-new-ticket-type-create-prompt", "ticketing.add-ticketing-page.no-ticket-type-options-message", "ticketing.add-ticketing-page.add-new-ticket-type-helper", "ticketing.add-ticketing-page.add-new-ticket-type", "ticketing.add-ticketing-page.ticket-type-tooltip", "ticketing.add-ticketing-page.ticket-type-info-title", "ticketing.add-ticketing-page.type-to-create-new-ticket-type", "ticketing.add-ticketing-page.ticket-pricing-help-text", "ticketing.add-ticketing-page.ticket-quantity-help-text", "ticketing.add-ticketing-page.error-fetching-ticket-types", "ticketing.add-ticketing-page.time-zone", "ticketing.timezone-selection.label", "ticketing.timezone-selection.section-heading", "ticketing.onboarding.ticketing-settings.error-saving-settings", "ticketing.add-ticketing-page.select-time-zone-placeholder", "ticketing.onboarding.ticketing-settings.select-time-zone-placeholder", "ticketing.onboarding.ticketing-settings.error.loading-school", "ticketing.onboarding.admin-request.error.loading-requests", "ticketing.onboarding.admin-request.added-on", "ticketing.add-ticketing-page.save-as-draft-modal.instructions", "ticketing.scanner-access-code-modal.access-code-title", "ticketing.add-ticketing-page.save-as-draft-modal.title", "ticketing.add-ticketing-page.save-as-draft-modal.discard-changes", "ticketing.add-ticketing-page.save-as-draft-modal.save-changes", "ticketing.add-ticketing-page.save-as-draft-modal.error-saving-as-draft", "ticketing.add-ticketing-page.date-and-time-help-text", "ticketing.add-ticketing-page.quantity-less-than-existing", "ticketing.add-ticketing-page.reserved-seating.venue-selection-description", "ticketing.add-ticketing-page.reserved-seating.venue-selector-label", "ticketing.add-ticketing-page.reserved-seating.venue-selector-empty", "ticketing.add-ticketing-page.reserved-seating.venue-selector-instruction", "ticketing.add-ticketing-page.reserved-seating.venue-configuration-selector-label", "ticketing.add-ticketing-page.reserved-seating.venue-configuration-selector-empty", "ticketing.add-ticketing-page.reserved-seating.venue-configuration-selector-instruction", "ticketing.add-ticketing-page.review.event-date-information", "ticketing.add-ticketing-page.review.review-item", "ticketing.add-ticketing-page.review.event-description", "ticketing.add-ticketing-page.review.event-title", "ticketing.add-ticketing-page.review.ticket-type-data", "ticketing.add-ticketing-page.review.ticket-type-data-with-price", "ticketing.add-ticketing-page.review.header.event-type", "ticketing.add-ticketing-page.review.general-admission", "ticketing.add-ticketing-page.review.reserved-seating", "ticketing.add-ticketing-page.review.header.venue", "ticketing.add-ticketing-page.review.header.seating-layout", "ticketing.add-ticketing-page.review.header.pass-sections", "ticketing.add-ticketing-page.review.ticket-types-line-prefix", "ticketing.add-ticketing-page.review.button.update-event-type", "ticketing.add-ticketing-page.review.button.update-event-details", "ticketing.add-ticketing-page.review.button.update-tickets", "ticketing.add-ticketing-page.event-information-schedule-entry-placeholder", "ticketing.add-ticketing-page.event-information-schedule-entry-error-help-text", "ticketing.add-ticketing-page.event-information-schedule-entry-placeholder.no-options", "ticketing.form-step.form-type", "ticketing.form-step.event-information", "ticketing.form-step.events-by-team", "ticketing.form-step.events-by-teams", "ticketing.form-step.ticket-pricing", "ticketing.form-step.pass-seating", "ticketing.form-step.pass-details", "ticketing.form-step.review", "ticketing.form-step.header", "ticketing.form-step.header.event-type", "ticketing.form-step.header.ticketed-event-type", "ticketing.form-step.header.pass-type", "ticketing.form-step.header.setup-pass-pricing", "ticketing.form-step.header.event-information", "ticketing.form-step.header.pass-details", "ticketing.form-step.header.team-and-event-information", "ticketing.form-step.header.setup-ticket-pricing", "ticketing.form-step.header.review-information", "ticketing.form-step.form-type.subheader", "ticketing.form-step.event-type.general-admission-header", "ticketing.form-step.event-type.general-admission-description", "ticketing.form-step.event-type.general-admission-description-tickets", "ticketing.form-step.event-type.general-admission-description-passes", "ticketing.form-step.event-type.reserved-seating-header", "ticketing.form-step.event-type.reserved-seating-description", "ticketing.form-step.event-type.cannot-change-type-warning", "ticketing.form-step.event-type.no-venues-set-up", "ticketing.form-step.event-type.no-seating-charts-set-up", "ticketing.venue-selection.error-fetching-venues", "ticketing.form-step.event-type.no-seating-charts-set-up-singular", "ticketing.form-step.event-selection.custom-event", "ticketing.form-step.event-selection.custom-event-description", "ticketing.form-step.event-selection.scheduled-event", "ticketing.form-step.event-selection.scheduled-event-description", "ticketing.form-step.event-type", "ticketing.form-step.header.event-selection", "ticketing.form-step.header.event-selection.review-header", "ticketing.form-step.header.event-selection.review-cta", "ticketing.create-pass-page.cannot-change-team-warning", "ticketing.create-pass-page.cannot-change-teams-warning", "ticketing.create-pass-page.reserved-seating.venue-selection-description", "ticketing.create-pass-page.reserved-seating-description", "ticketing.create-pass-page.cannot-change-type-warning", "ticketing.create-pass-page.save-as-draft-toast.error-saving-as-draft", "ticketing.create-pass-page.create-pass", "ticketing.create-pass-page.review-pass", "ticketing.create-pass-page.pass-information-header", "ticketing.create-pass-page.pass-information", "ticketing.create-pass-page.pass-name", "ticketing.create-pass-page.pass-name-instruction", "ticketing.create-pass-page.pass-name-character-limit", "ticketing.create-pass-page.pass-description", "ticketing.create-pass-page.pass-description-instruction", "ticketing.create-pass-page.pass-description-character-limit", "ticketing.create-pass-page.pass-character-limit-error", "ticketing.create-pass-page.start-date-label", "ticketing.create-pass-page.end-date-label", "ticketing.create-pass-page.pass-access-details-label", "ticketing.create-pass-page.pass-access-details-description", "ticketing.create-pass-page.pass-access-details-description-single-team", "ticketing.create-pass-page.team-selector-label", "ticketing.create-pass-page.teams-selector-label", "ticketing.create-pass-page.team-selector-placeholder-ga", "ticketing.create-pass-page.team-selector-placeholder-rs", "ticketing.create-pass-page.pass-price-details-label", "ticketing.create-pass-page.pass-price-details-description", "ticketing.create-pass-page.pass-price-label", "ticketing.create-pass-page.pass-disclaimer", "ticketing.create-pass-page.success-publishing-pass-config", "ticketing.create-pass-page.error-publishing-pass-config", "ticketing.create-pass-page.team-selector-empty", "ticketing.create-pass-page.end-date-error", "ticketing.create-pass-page.success-saving-draft-pass-config", "ticketing.create-pass-page.pass-quantity-label", "ticketing.create-pass-page.pass-quantity-placeholder", "ticketing.create-pass-page.pass-quantity-error", "ticketing.create-pass-page.quantity-help-text", "ticketing.create-pass-page.quantity-less-than-existing", "ticketing.create-pass-page.review.pass-name", "ticketing.create-pass-page.review.active-time-period", "ticketing.create-pass-page.review.pass-description", "ticketing.create-pass-page.review.header.teams-included", "ticketing.create-pass-page.review.header.single-team-included", "ticketing.create-pass-page.review.header.pass-details", "ticketing.create-pass-page.review.header.seating-information", "ticketing.create-pass-page.review.button.edit-details", "ticketing.create-pass-page.review.button.edit-teams", "ticketing.create-pass-page.review.button.edit-single-team", "ticketing.create-pass-page.review.button.edit-seating", "ticketing.create-pass-page.pass-name-error-help-text", "ticketing.scanner-access-code-modal.header", "ticketing.scanner-access-code-modal.copy-code-button", "ticketing.scanner-access-code-modal.error-text", "ticketing.scanner-access-code-modal.instructions", "ticketing.home-page.copy-access-code.code-copied", "ticketing.home-page.copy-access-code.code-copy-failed", "ticketing.pass-details-page.pass-information", "ticketing.pass-details-page.active-time-period", "ticketing.pass-details-page.pass-description", "ticketing.pass-details-page.pass-pricing", "ticketing.pass-details-page.pricing", "ticketing.pass-details-page.quantity", "ticketing.pass-details-page.error-loading-pass-details", "ticketing.pass-details-page.pass-events-header-name", "ticketing.pass-details-page.pass-events-header-date-time", "ticketing.pass-details-page.pass-events-header-gender-level-sport", "ticketing.pass-details-page.events-headline", "ticketing.pass-details-page.events-empty-state", "ticketing.pass-sections-selector.already-published-note", "ticketing.pass-sections-selector.select-all-sections", "ticketing.pass-sections-selector.sections-available", "ticketing.pass-sections-selector.sections-available-description", "ticketing.pass-sections-selector.add-sections-for-more-options", "ticketing.pass-sections-selector.sections-selector-label", "ticketing.pass-sections-selector.section-selector-empty", "ticketing.pass-sections-selector.sections-selector-instruction", "ticketing.pass-sections-selector.published-seating-sections", "ticketing.ticket-type-modal.ticket-type-name", "ticketing.ticket-type-modal.ticket-type-name-character-limit", "ticketing.ticketed-event-details.event-info.header", "ticketing.ticketed-event-details.event-info.date", "ticketing.ticketed-event-details.event-info.description-header", "ticketing.ticketed-event-details.last-updated-text", "ticketing.ticketed-event-details.error-fetching-data-for-edit-function", "ticketing.ticketed-event-details.ticketing-summary.header", "ticketing.ticketed-event-details.ticketing-summary.info-text", "ticketing.ticketed-event-details.ticketing-summary.statistics.tickets-sold", "ticketing.ticketed-event-details.ticketing-summary.statistics.estimated-revenue", "ticketing.ticketed-event-details.ticketing-summary.statistics.tickets-scanned", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.ticket-type", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.price-per-ticket", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.tickets-sold", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.comp-tickets", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.quantity-available", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.revenue", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.digital-sales", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.cash-sales", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.footer.total-revenue", "ticketing.ticketed-event-details.ticketing-summary.attendance.header", "ticketing.ticketed-event-details.ticketing-summary.attendance-table.header.ticket-type", "ticketing.ticketed-event-details.ticketing-summary.attendance-table.header.tickets-scanned", "ticketing.ticketed-event-details.ticketing-summary.attendance-table.header.tickets-sold", "ticketing.ticketed-event-details.ticketing-summary.attendance-table.rows.pass-holder", "ticketing.ticketed-event-details.ticketing-summary.attendance-table.footer.total-tickets-scanned", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.no-limit", "ticketing.ticketed-event-details.ticketing-summary.estimated-revenue.header", "ticketing.ticketed-event-details.ticketing-summary.estimated-revenue.info-text", "ticketing.ticketed-event-details.ticketing-summary.estimated-revenue.stripe-dashboard-link", "ticketing.ticketed-event-details.error.note", "ticketing.purchaser-list.empty-state-headline", "ticketing.purchaser-list.empty-state-text", "ticketing.purchaser-list.point-of-sale-help-text", "ticketing.purchaser-list.reserved-seating-header", "ticketing.purchaser-list.download", "ticketing.purchaser-list.empty-state-headline-passes", "ticketing.purchaser-list.empty-state-text-passes", "ticketing.purchaser-list.empty-state-headline-tickets", "ticketing.purchaser-list.empty-state-text-tickets", "ticketing.purchaser-list.empty-state-headline-comp-tickets", "ticketing.purchaser-list.empty-state-text-comp-tickets", "ticketing.purchaser-list.empty-state-headline-comp-passes", "ticketing.purchaser-list.empty-state-text-comp-passes", "ticketing.purchaser-list.ticket-types-purchased-header", "ticketing.download-purchaser-list-modal.header", "ticketing.download-purchaser-list-modal.subhead", "ticketing.download-purchaser-list-modal.download-button.csv", "ticketing.download-purchaser-list-modal.download-button.pdf", "ticketing.download-purchaser-list-modal.include-fields-header", "ticketing.download-purchaser-list-modal.options-header", "ticketing.download-purchaser-list-modal.checkbox-label.first-name", "ticketing.download-purchaser-list-modal.checkbox-label.last-name", "ticketing.download-purchaser-list-modal.checkbox-label.email", "ticketing.download-purchaser-list-modal.checkbox-label.reserved-seats", "ticketing.download-purchaser-list-modal.checkbox-label.order-number", "ticketing.download-purchaser-list-modal.checkbox-label.time-of-purchase", "ticketing.download-purchaser-list-modal.checkbox-label.amount-purchased", "ticketing.download-purchaser-list-modal.checkbox-label.purchase-source", "ticketing.download-purchaser-list-modal.checkbox-label.payment-type", "ticketing.download-purchaser-list-modal.checkbox-label.ticket-types-purchased", "ticketing.download-purchaser-list-modal.checkbox-label.options.exclude-no-name-email", "ticketing.download-purchaser-list-modal.error-loading-list", "ticketing.download-purchaser-list-modal.error-downloading-list", "ticketing.download-purchaser-list-modal.exclude-complimentary", "ticketing.ticketed-event-details.purchaser-name", "ticketing.ticketed-event-details.purchaser-email", "ticketing.ticketed-event-details.purchaser-order-number", "ticketing.ticketed-event-details.time-of-purchase", "ticketing.ticketed-event-details.quantity-purchased", "ticketing.ticketed-event-details.number-tickets", "ticketing.ticketed-event-details.number-passes", "ticketing.ticketed-event-details.purchase-source", "ticketing.ticketed-event-details.purchaser-payment-type", "ticketing.add-ticket-type-modal.error-saving-ticket-type", "ticketing.add-ticket-type-modal.add-to-section", "ticketing.add-ticket-type-modal.select-sections", "ticketing.add-ticket-type-modal.select-help-text", "ticketing.add-ticket-type-modal.add-ticket-type-to-section", "ticketing.ticket-types-for-category.published-ticket-types", "ticketing.ticket-types-for-category.ticket-types-select-label", "ticketing.ticket-types-for-category.ticket-types-select-placeholder", "ticketing.ticket-types-for-category.ticket-types-by-section", "ticketing.ticket-types-for-category.already-published-note", "ticketing.ticket-types-for-category.create-ticket-type", "ticketing.ticket-types-for-category.no-sections", "ticketing.ticket-types-for-category.no-sections-contact-support", "ticketing.ticket-types-for-category.add-ticket-type-for-more-options", "ticketing.category-item.section-name", "ticketing.visibility.tickets-title", "ticketing.visibility.pass-title", "ticketing.visibility.tickets-subtitle", "ticketing.visibility.pass-subtitle", "ticketing.visibility.public", "ticketing.visibility.private", "ticketing.visibility.not-for-sale", "ticketing.form-fields.additional-fan-information.header", "ticketing.form-fields.additional-fan-information.subhead", "ticketing.form-fields.add-form-field", "ticketing.form-fields.add-form-field.max-form-fields-error-message", "ticketing.form-fields.select.label", "ticketing.form-fields.select.placeholder", "ticketing.form-fields.select.empty-message", "ticketing.form-fields.item.required", "ticketing.form-fields.item.required.yes", "ticketing.form-fields.item.required.no", "ticketing.form-fields.item.help-text", "ticketing.form-fields.add-field-modal.header", "ticketing.form-fields.add-field-modal.name.label", "ticketing.form-fields.add-field-modal.name.placeholder", "ticketing.form-fields.add-field-modal.name.character-limit", "ticketing.form-fields.add-field-modal.help-text.label", "ticketing.form-fields.add-field-modal.help-text.placeholder", "ticketing.form-fields.add-field-modal.help-text.character-limit", "ticketing.form-fields.add-field-modal.required.label", "ticketing.form-fields.add-field-modal.required.yes", "ticketing.form-fields.add-field-modal.required.no", "ticketing.form-fields.add-field-modal.add-button", "ticketing.form-fields.add-field-modal.toast.error", "ticketing.form-fields.review.header", "ticketing.form-fields.review.required", "ticketing.form-fields.error-fetching", "ticketing.tabs.events", "ticketing.tabs.published", "ticketing.tabs.non-ticketed", "ticketing.tabs.draft", "ticketing.tabs.current", "ticketing.tabs.active", "ticketing.tabs.past", "ticketing.tabs.passes", "ticketing.tabs.overview", "ticketing.tabs.purchaser-list", "ticketing.save-as-draft", "ticketing.review", "ticketing.publish", "ticketing.cancel", "ticketing.remove", "ticketing.continue", "ticketing.create", "ticketing.date", "ticketing.date.at.time", "ticketing.price", "ticketing.add-ticket-type.label", "ticketing.start-time", "ticketing.add-ticketing", "ticketing.create-pass", "ticketing.this-pass", "ticketing.retry", "ticketing.refresh", "ticketing.reload", "ticketing.contact-support", "ticketing.contact-support-lower", "ticketing.save", "ticketing.save-seats", "ticketing.add", "ticketing.update", "ticketing.back", "ticketing.manage", "ticketing.select-all", "ticketing.deselect-all", "ticketing.events", "ticketing.tickets", "ticketing.passes", "ticketing.ticket", "ticketing.pass", "ticketing.pass.upper", "ticketing.event", "ticketing.submit", "ticketing.event.upper", "ticketing.past-date-help-text", "ticketing.source.mobile", "ticketing.source.web", "ticketing.source.pos", "ticketing.source.complimentary", "ticketing.source.shared", "ticketing.source.transfer", "ticketing.source.manual-entry", "ticketing.payment-type.electronic", "ticketing.payment-type.external", "ticketing.item-type.ticket", "ticketing.item-type.pass", "ticketing.unlimited", "ticketing.none", "ticketing.teams", "ticketing.team", "ticketing.quantity-available", "ticketing.first-name", "ticketing.last-name", "ticketing.email", "ticketing.ticket-type", "ticketing.ticket-quantity", "ticketing.edit", "ticketing.enter-first-name", "ticketing.enter-last-name", "ticketing.enter-email-address", "ticketing.move-to-draft", "ticketing.provide-first-name", "ticketing.provide-last-name", "ticketing.provide-valid-email", "ticketing.seat-selection.section", "ticketing.seat-selection.row", "ticketing.seat-selection.seat", "ticketing.seat-selection.zone", "ticketing.seat-selection.seats-selected", "ticketing.seat-selection.select-seats", "ticketing.seat-selection.edit-seats", "ticketing.seat-selection.remove", "ticketing.seat-selection.seats-selected-count", "ticketing.complimentary.recipient-placeholder", "ticketing.complimentary.reserved-seating.tickets.hold-seats.title", "ticketing.complimentary.reserved-seating.passes.hold-seats.title", "ticketing.complimentary.reserved-seating.tickets.hold-seats.description", "ticketing.complimentary.reserved-seating.passes.hold-seats.description", "ticketing.complimentary.reserved-seating.hold-seats.get-started", "ticketing.complimentary.provide-first-name", "ticketing.complimentary.provide-last-name", "ticketing.complimentary.provide-valid-email", "ticketing.complimentary.provide-valid-quantity", "ticketing.complimentary.remove", "ticketing.complimentary.select-ticket-type", "ticketing.complimentary.add-recipient", "ticketing.complimentary.nav-button.title-tickets", "ticketing.complimentary.nav-button.title-tickets.mobile", "ticketing.complimentary.totals-tickets", "ticketing.complimentary.totals.recipients", "ticketing.complimentary.totals.title", "ticketing.complimentary.recipients-tickets", "ticketing.complimentary.confirm.send-tickets", "ticketing.complimentary.review.header-tickets", "ticketing.complimentary.review.sub-header-tickets", "ticketing.complimentary.edit.header-tickets", "ticketing.complimentary.edit.sub-header-tickets", "ticketing.complimentary.recipient-information-tickets", "ticketing.complimentary.recipient-information-passes", "ticketing.complimentary.send.confirm-tickets", "ticketing.complimentary.send.confirm-tickets.no-count", "ticketing.complimentary.send.fail-tickets", "ticketing.complimentary.nav-button.title-passes", "ticketing.complimentary.nav-button.title-passes.mobile", "ticketing.complimentary.totals-passes", "ticketing.complimentary.recipients-passes", "ticketing.complimentary.confirm.send-passes", "ticketing.complimentary.review.header-passes", "ticketing.complimentary.review.sub-header-passes", "ticketing.complimentary.edit.header-passes", "ticketing.complimentary.edit.sub-header-passes", "ticketing.complimentary.recipient-information-passes", "ticketing.complimentary.send.confirm-passes", "ticketing.complimentary.send.fail-passes", "ticketing.complimentary.send.fail.title", "ticketing.complimentary.return-to-event-details", "ticketing.seat-selection.selected-seats", "ticketing.complimentary.reserved-seating.timer.timeout", "ticketing.complimentary.reserved-seating.timer.display", "ticketing.complimentary.reserved-seating.timer.okay", "ticketing.complimentary.reserved-seating.click-to-enable-chart", "ticketing.complimentary.reserved-seating.modal.title", "ticketing.complimentary.reserved-seating.modal.subtitle", "ticketing.complimentary.csv.import-button", "ticketing.complimentary.csv.modal.header", "ticketing.complimentary.csv.modal.subtitle", "ticketing.complimentary.csv.modal.import-button", "ticketing.complimentary.csv.modal.error.wrong-file-type", "ticketing.complimentary.csv.modal.error.no-rows", "ticketing.complimentary.csv.modal.error.invalid-file", "ticketing.complimentary.csv.modal.download-instruction", "ticketing.complimentary.csv.modal.upload-instruction", "ticketing.complimentary.csv.modal.download-template-button", "ticketing.complimentary.csv.modal.upload-file-button", "ticketing.complimentary.csv.modal.review.filename", "ticketing.complimentary.csv.modal.review.recipient-count", "ticketing.complimentary.csv.modal.review.ticket-type-heading", "ticketing.complimentary.csv.modal.review.ticket-type-sub", "ticketing.fee-strategy.example-text.ticket", "ticketing.fee-strategy.example-text.pass", "ticketing.fee-strategy.example-text.bold-1", "ticketing.fee-strategy.example-text.bold-2", "ticketing.fee-strategy.who-covers-fees", "ticketing.fee-strategy.who-covers-fees-link-event", "ticketing.fee-strategy.who-covers-fees-link-pass", "ticketing.fee-strategy.who-covers-fees-link-purchases", "ticketing.fee-strategy.ticket-price-minimum", "ticketing.fee-strategy.ticket-price-minimum-RS", "ticketing.fee-strategy.pass-price-minimum", "ticketing.fee-strategy.fees-covered-by", "ticketing.fee-strategy.fees-covered-by.your-fans", "ticketing.fee-strategy.fees-covered-by.your-organization", "ticketing.fee-strategy.applied-at-checkout", "ticketing.fee-strategy.deducted-from-revenue", "ticketing.visibility", "ticketing.status", "ticketing.my-organizations.error-fetching", "ticketing.not-org-admin-of-current-org", "ticketing.org-ticketing-enabled.error-fetching", "ticketing.org-ticketing-enabled.not-enabled", "ticketing.publish-events.publishing-disabled-by-payment-status", "ticketing.publish-passes.publishing-disabled-by-payment-status", "ticketing.schedule-reference-uploader.error.loading-teams", "ticketing.schedule-reference-uploader.header", "ticketing.schedule-reference-uploader.subheading", "ticketing.schedule-reference-uploader.instructions.info-to-include", "ticketing.schedule-reference-uploader.instructions.team", "ticketing.schedule-reference-uploader.instructions.date", "ticketing.schedule-reference-uploader.instructions.start-time", "ticketing.schedule-reference-uploader.instructions.opponent", "ticketing.schedule-reference-uploader.instructions.location", "ticketing.schedule-reference-uploader.instructions.recommended-file-name", "ticketing.schedule-reference-uploader.drag-drop.drop-here-or", "ticketing.schedule-reference-uploader.drag-drop.browse", "ticketing.schedule-reference-uploader.drag-drop.upload-error.file-type", "ticketing.schedule-reference-uploader.drag-drop.upload-error.file-size", "ticketing.schedule-reference-uploader.drag-drop.upload-error.file-count", "ticketing.schedule-reference-uploader.drag-drop.upload-error.duplicate", "ticketing.schedule-reference-uploader.drag-drop.instruction-note", "ticketing.schedule-reference-uploader.file-list.header", "ticketing.schedule-reference-uploader.team-select.label", "ticketing.schedule-reference-uploader.team-select.placeholder", "ticketing.schedule-reference-uploader.notes.label", "ticketing.schedule-reference-uploader.notes.placeholder", "ticketing.schedule-reference-uploader.notes.character-limit", "ticketing.schedule-reference-uploader.error-toast.content", "ticketing.upload", "ticketing.default-ticket-types.headline", "ticketing.default-ticket-types.subhead", "ticketing.default-ticket-types.ticket-types", "ticketing.default-ticket-types.error-state", "ticketing.team-ticket-type-input.ticket-name", "ticketing.team-ticket-type-input.ticket-name-placeholder", "ticketing.team-ticket-type-input.teams", "ticketing.team-ticket-type-input.teams-placeholder", "ticketing.team-ticket-type-input.add-ticket-type", "ticketing.team-ticket-type-input.team-tooltip", "ticketing.duplicate", "ticketing.team-ticket-type-input.team-tooltip", "ticketing.payout-selection.header", "ticketing.payout-selection.subheader", "ticketing.payout-selection.error-loading-page", "ticketing.payout-selection.direct-deposits.label", "ticketing.payout-selection.direct-deposits.description", "ticketing.payout-selection.direct-deposits.incomplete.header", "ticketing.payout-selection.direct-deposits.incomplete.message", "ticketing.payout-selection.direct-deposits.incomplete.footer", "ticketing.payout-selection.direct-deposits.complete.header", "ticketing.payout-selection.direct-deposits.needs-review.header", "ticketing.payout-selection.direct-deposits.needs-review.message", "ticketing.payout-selection.direct-deposits.complete.message", "ticketing.payout-selection.direct-deposits.in-review.header", "ticketing.payout-selection.direct-deposits.in-review.message", "ticketing.payout-selection.check.label", "ticketing.payout-selection.check.description", "ticketing.payout-selection.check.payee-information", "ticketing.payout-selection.check.setup-in-tipalti", "ticketing.payout-selection.check.setup-in-tipalti-header", "ticketing.payout-selection.check.getting-setup-in-tipalti-header", "ticketing.payout-selection.check.getting-setup-in-tipalti", "ticketing.payout-selection.direct-deposits.go-to-stripe", "ticketing.payout-selection.check.rev-share-email", "ticketing.onboarding", "ticketing.onboarding.step.ticketing-settings", "ticketing.onboarding.step.ticketing-settings.description", "ticketing.onboarding.step.ticketing-settings.cta", "ticketing.onboarding.step.payment-details", "ticketing.onboarding.step.payment-details.description", "ticketing.onboarding.step.payment-details.cta", "ticketing.onboarding.step.payment-details.info-text.direct-deposit", "ticketing.onboarding.step.payment-details.info-text.check", "ticketing.onboarding.step.payment-details.info-text.direct-deposit-review", "ticketing.onboarding.step.payment-details.info-text.direct-deposit-info-needed", "ticketing.onboarding.step.schedule-upload", "ticketing.onboarding.step.schedule-upload.description", "ticketing.onboarding.step.schedule-upload.cta", "ticketing.onboarding.step.ticket-types", "ticketing.onboarding.step.ticket-types.description", "ticketing.onboarding.step.ticket-types.cta", "ticketing.onboarding.step.ticket-types.info-text", "ticketing.onboarding.request-users", "ticketing.invite", "ticketing.onboarding.complete-onboarding", "ticketing.onboarding.header", "ticketing.onboarding.subheader", "ticketing.report-cash-sales.title", "ticketing.report-cash-sales.helper-text", "ticketing.report-cash-sales.ticket-type", "ticketing.report-cash-sales.ticketsSold", "ticketing.report-cash-sales.ticketPrice", "ticketing.report-cash-sales.cashSales", "ticketing.report-cash-sales.update", "ticketing.report-cash-sales.totalCashSales", "ticketing.report-cash-sales.error", "ticketing.settings", "ticketing.settings.header", "ticketing.settings.subheader", "ticketing.settings.payout-selection.direct-deposit.header", "ticketing.settings.payout-selection.direct-deposit.under-review", "ticketing.settings.payout-selection.direct-deposit.under-review.message", "ticketing.settings.payout-selection.header", "ticketing.settings.payout-selection.check.help-text", "ticketing.settings.payout-selection.check.submitted", "ticketing.badge.public", "ticketing.badge.private", "ticketing.badge.league", "ticketing.badge.not-for-sale", "ticketing.badge.renewal", "ticketing.badge.unknown", "ticketing.event-details-change-modal.header", "ticketing.pass-details-change-modal.header", "ticketing.pass-details-change-modal.warnings.dates-changed", "ticketing.pass-details-change-modal.warnings.teams-changed", "ticketing.pass-details-change-modal.warnings.teams-changed-and-dates-changed", "ticketing.pass-details-change-modal.warnings.generic", "ticketing.event-time-change-modal.details", "ticketing.event-time-change-modal.action.update"]}}, "base-language": {"ticketing.home-page.hudl-ticketing": "Ticketing", "ticketing.home-page.ticketing-menu": "Ticketing Menu", "ticketing.home-page.create-button": "Create", "ticketing.home-page.create-pass": "Pass", "ticketing.home-page.no-scheduled-events": "No events scheduled.", "ticketing.home-page.schedule-upcoming-events": "Schedule your upcoming events to add digital ticketing here.", "ticketing.home-page.no-published-events": "No published ticketed events.", "ticketing.home-page.no-draft-events": "No draft ticketed events.", "ticketing.home-page.no-past-events": "No past ticketed events.", "ticketing.home-page.publish-ticketed-event": "Create and publish a ticketed event to display here.", "ticketing.home-page.draft-ticketed-event": "Save a ticketed event as a draft to display here.", "ticketing.home-page.past-ticketed-event": "Currently there are no past events. When events end, they will automatically display here.", "ticketing.home-page.draft-pass": "Save a pass as a draft to display here.", "ticketing.home-page.upcoming-events": "Upcoming Events", "ticketing.home-page.non-ticketed-events": "Non-Ticketed Events", "ticketing.home-page.draft-events": "Draft Events", "ticketing.home-page.past-events": "Past Events", "ticketing.home-page.active-passes": "Active Passes", "ticketing.home-page.current-passes": "Current Passes", "ticketing.home-page.draft-passes": "Draft Passes", "ticketing.home-page.past-passes": "Past Passes", "ticketing.home-page.no-past-passes": "No past passes.", "ticketing.home-page.past-passes-text": "Currently there are no past passes. When passes end, they will automatically display here.", "ticketing.home-page.coming-soon": "Coming Soon", "ticketing.home-page.coming-soon-passes": "Pass creation and management will be available soon. Check back later.", "ticketing.home-page.past-events-unavailable": "Past events currently unavailable.", "ticketing.home-page.error-finding-school": "There was an error finding your organization.", "ticketing.home-page.error-finding-schedule-entries": "There was an error finding your scheduled events.", "ticketing.home-page.error-finding-ticketed-events": "There was an error finding your upcoming events.", "ticketing.home-page.error-fetching-account-status": "There was an error fetching your account status. Please refresh the page.", "ticketing.home-page.error-finding-passes": "There was an error fetching your passes.", "ticketing.home-page.error-non-us-school": "Ticketing is not available in your region at this time. Contact support for more information.", "ticketing.home-page.load-more": "Load More", "ticketing.home-page.your-ticketed-event": "Your ticketed event", "ticketing.home-page.your-pass": "Your pass", "ticketing.home-page.share-pass": "Share Pass", "ticketing.home-page.share-pass.instructions": "Share your pass with the link below. You can click \"Copy Link\" or right-click + \"Copy\" to share it.", "ticketing.home-page.share-event": "Share Event", "ticketing.home-page.share-event.instructions": "Share your ticketed event with the link below. You can click \"Copy Link\" or right-click + \"Copy\" to share it.", "ticketing.home-page.share-event.copy-link": "Copy Link", "ticketing.home-page.share-event.link-copied": "<PERSON> Copied to Clipboard", "ticketing.home-page.share-event.link-copy-failed": "Could not copy link to clipboard. Try manually copying the link.", "ticketing.home-page.payouts-analytics-action": "Payouts & Analytics", "ticketing.home-page.scanner-access-code-action": "Ticket Scanning Access", "ticketing.home-page.event-action-dropdown.share": "Share", "ticketing.home-page.event-action-dropdown.edit": "Edit", "ticketing.home-page.event-action-dropdown.reportCashSales": "Report Cash Sales", "ticketing.home-page.no-passes": "No published passes.", "ticketing.home-page.no-draft-passes": "No draft passes.", "ticketing.home-page.publish-passes-empty": "Create and publish passes to display here.", "ticketing.home-page.passes-header-pass": "Pass", "ticketing.home-page.passes-header-active-time-period": "Active Time Period", "ticketing.home-page.passes-header-passes-sold": "Passes Sold", "ticketing.home-page.passes-header-teams": "Team(s)", "ticketing.home-page.submit-schedules": "Submit Schedules", "ticketing.tooltip.no-backdoored-users": "This feature is not accessible to backdoored users.", "ticketing.onboarding.header-title": "Welcome to Ticketing", "ticketing.onboarding.header-subtitle": "Please complete the required steps below to start using ticketing.", "ticketing.onboarding.finish-button": "Finish", "ticketing.onboarding.stripe-account.title": "Payout Information", "ticketing.onboarding.stripe-account.description": "To receive payouts, add your account information through Stripe Connect.", "ticketing.onboarding.stripe-account.button": "Add Payout Information", "ticketing.onboarding.terms-of-service.title": "Terms of Service Agreement", "ticketing.onboarding.terms-of-service.description": "Read and sign the terms of service agreement.", "ticketing.onboarding.terms-of-service.button": "View Agreement", "ticketing.onboarding.tos-modal.header": "Terms of Service Agreement", "ticketing.onboarding.tos-modal.subhead": "Scroll to read all terms before signing and accepting.", "ticketing.onboarding.tos-modal.decline-button": "Decline", "ticketing.onboarding.tos-modal.agree-button": "Agree", "ticketing.onboarding.tos-modal.signature-name": "Signature", "ticketing.onboarding.tos-modal.error.loading-agreement": "There was an error loading the terms of service agreement. Please refresh the page.", "ticketing.onboarding.tos-modal.error.submitting-agreement": "There was an error submitting the Terms of Service agreement. Please try again.", "ticketing.onboarding.ticketing-settings.select-time-zone-placeholder": "Select default timezone", "ticketing.onboarding.ticketing-settings.error.loading-school": "There was an error fetching your organization details. Please try again.", "ticketing.onboarding.ticketing-settings.page-title": "Ticketing Settings", "ticketing.onboarding.ticketing-settings.page-subtitle": "Set up your default timezone and fee strategy. You can always adjust these settings later.", "ticketing.onboarding.ticketing-settings.timezone-section-title": "Default Timezone", "ticketing.onboarding.ticketing-settings.cancel-button": "Cancel", "ticketing.onboarding.ticketing-settings.save-button": "Save", "ticketing.onboarding.ticketing-settings.error-saving-settings": "There was an error saving your settings.", "ticketing.onboarding.admin-request.page-title": "Give User Access", "ticketing.onboarding.admin-request.page-subtitle": "Add additional users as ticketing admins. They’ll be able to set up, manage and share ticketed events and passes.", "ticketing.onboarding.admin-request.user-information-section-title": "User Information", "ticketing.onboarding.admin-request.add-another": "Add Another User", "ticketing.onboarding.admin-request.error-saving": "There was an error saving admin request details.", "ticketing.onboarding.admin-request.error.loading-requests": "There was an error loading admin request details.", "ticketing.onboarding.admin-request.added-on": "Added on {date}", "ticketing.onboarding.admin-request.users-added-section-title": "Users Added", "ticketing.home-page.tickets-sold": "Tickets Sold: {ticketsSold}", "ticketing.add-ticketing-page.add-ticketing": "Add Ticketing to Your Event", "ticketing.add-ticketing-page.review-ticketing": "Review Ticketing for Your Event", "ticketing.add-ticketing-page.update-ticketing": "Update Ticketing for Your Event", "ticketing.add-ticketing-page.scheduled-event": "Scheduled Event", "ticketing.add-ticketing-page.event-details-instructions": "Give your fans some more details about the event. They'll be able to see this information on the event ticketing page.", "ticketing.add-ticketing-page.event-title": "Event Title", "ticketing.add-ticketing-page.event-title-error-help-text": "Event title is required.", "ticketing.add-ticketing-page.event-description": "Event Description", "ticketing.add-ticketing-page.event-information": "Event Information", "ticketing.add-ticketing-page.ticketed-event-details": "Ticketed Event Details", "ticketing.add-ticketing-page.review-event-information": "Review the scheduled event information. To make changes, update the event from the team's schedule page before adding digital ticketing.", "ticketing.add-ticketing-page.event-description-placeholder": "Describe the event (e.g., occasion, venue, location).", "ticketing.add-ticketing-page.disclaimer": "Make sure the information entered is correct. Once you publish, {itemType} will become available to fans.", "ticketing.add-ticketing-page.ticket-pricing": "Ticket Pricing", "ticketing.add-ticketing-page.ticket-pricing-description": "Enter the ticket options fans can purchase for this event. Make sure to fill out all required (*) fields to publish.", "ticketing.add-ticketing-page.ticket-pricing-description-required-fields": "Enter the ticket options fans can purchase for this event. Make sure to fill out all required (*) fields to publish or save as draft.", "ticketing.add-ticketing-page.quantity-for-sale": "Quantity for Sale", "ticketing.add-ticketing-page.quantity-error-help-text": "Quantity must be greater than 0.", "ticketing.add-ticketing-page.quantity-help-text": "If no quantity is set, it will be unlimited.", "ticketing.add-ticketing-page.quantity-placeholder": "Enter quantity", "ticketing.add-ticketing-page.type.hardcoded-free-admission": "Free Admission", "ticketing.add-ticketing-page.error-publishing-ticketed-event": "There was an error publishing your ticketed event.", "ticketing.add-ticketing-page.success-publishing-ticketed-event": "{ticketedEventName} was published successfully.", "ticketing.add-ticketing-page.error-finding-ticketed-event": "There was an error displaying your ticketed event.", "ticketing.add-ticketing-page.success-saving-draft-ticketed-event": "{ticketedEventName} was successfully saved as a draft.", "ticketing.add-ticketing-page.event-title-character-limit": "Character limit: {charactersUsed}/{formTitleMaxLength}", "ticketing.add-ticketing-page.event-description-help-text": "Character limit: {descriptionCharactersUsed}/{descriptionMaxLength}", "ticketing.add-ticketing-page.select-ticket-price-option": "Select ticket price option", "ticketing.add-ticketing-page.add-new-ticket-type": " Add New Ticket Type", "ticketing.add-ticketing-page.ticket-type-tooltip": "You can add a new ticket type at any time by typing a custom name.", "ticketing.add-ticketing-page.ticket-type-info-title": "Add Ticket Type Info", "ticketing.add-ticketing-page.add-new-ticket-type-create-prompt": "Add {ticketTypeName} as a new ticket type", "ticketing.add-ticketing-page.no-ticket-type-options-message": "Start typing to add a new ticket type.", "ticketing.add-ticketing-page.add-new-ticket-type-helper": "This will allow you to set and use this ticket type in the future.", "ticketing.add-ticketing-page.type-to-create-new-ticket-type": "Type the name of the ticket type to create a new one.", "ticketing.add-ticketing-page.ticket-pricing-help-text": "Select or add a new ticket type to set the price.", "ticketing.add-ticketing-page.ticket-quantity-help-text": "Select or add a new ticket type to set the quantity.", "ticketing.add-ticketing-page.error-fetching-ticket-types": "There was an error fetching your saved ticket types. Please refresh the page.", "ticketing.add-ticketing-page.time-zone": "Time Zone", "ticketing.timezone-selection.label": "Select Timezone", "ticketing.timezone-selection.section-heading": "Default Timezone", "ticketing.add-ticketing-page.select-time-zone-placeholder": "Select event time zone", "ticketing.add-ticketing-page.save-as-draft-modal.instructions": "We noticed you have made some changes to this {itemType}. Would you like to save those changes?", "ticketing.add-ticketing-page.save-as-draft-modal.title": "Save Changes", "ticketing.add-ticketing-page.save-as-draft-modal.discard-changes": "Discard Changes", "ticketing.add-ticketing-page.save-as-draft-modal.save-changes": "Yes, Save", "ticketing.add-ticketing-page.save-as-draft-modal.error-saving-as-draft": "There was an error saving your draft event.", "ticketing.add-ticketing-page.date-and-time-help-text": "Any changes made here to the date or time will only update the ticketed event for fans—not the team's schedule entry.", "ticketing.add-ticketing-page.quantity-less-than-existing": "Quantity is less than the original amount. This ticket type may become sold out.", "ticketing.add-ticketing-page.reserved-seating.venue-selection-description": "Before adding tickets, choose a venue and the seating layout for this event. If you need to make changes to a venue or its seating layout, please {contactSupport}.", "ticketing.add-ticketing-page.reserved-seating.venue-selector-label": "Venue", "ticketing.add-ticketing-page.reserved-seating.venue-selector-empty": "No venues available", "ticketing.add-ticketing-page.reserved-seating.venue-selector-instruction": "Select venue", "ticketing.add-ticketing-page.reserved-seating.venue-configuration-selector-label": "Seating Layout", "ticketing.add-ticketing-page.reserved-seating.venue-configuration-selector-empty": "No seating layouts available", "ticketing.add-ticketing-page.reserved-seating.venue-configuration-selector-instruction": "Select seating layout", "ticketing.add-ticketing-page.review.event-date-information": "Date & Start Time", "ticketing.add-ticketing-page.review.event-description": "Description", "ticketing.add-ticketing-page.review.event-title": "Event Title", "ticketing.add-ticketing-page.review.review-item": "Review {item}", "ticketing.add-ticketing-page.review.ticket-type-data": "Price: ${price} | Quantity: {quantity}", "ticketing.add-ticketing-page.review.ticket-type-data-with-price": "Price: {price} | Quantity: {quantity}", "ticketing.add-ticketing-page.review.header.event-type": "Event Type", "ticketing.add-ticketing-page.review.general-admission": "General Admission", "ticketing.add-ticketing-page.review.reserved-seating": "Reserved Seating", "ticketing.add-ticketing-page.review.header.venue": "Venue", "ticketing.add-ticketing-page.review.header.seating-layout": "Seating Layout", "ticketing.add-ticketing-page.review.header.pass-sections": "Pass Section(s)", "ticketing.add-ticketing-page.review.ticket-types-line-prefix": "Ticket Type(s)", "ticketing.add-ticketing-page.review.button.update-event-type": "Edit Type", "ticketing.add-ticketing-page.review.button.update-event-details": "Edit Details", "ticketing.add-ticketing-page.review.button.update-tickets": "Edit Tickets", "ticketing.add-ticketing-page.event-information-schedule-entry-placeholder": "Select a schedule entry", "ticketing.add-ticketing-page.event-information-schedule-entry-error-help-text": "There was an error loading the schedule entries. Please refresh to try again.", "ticketing.add-ticketing-page.event-information-schedule-entry-placeholder.no-options": "No schedule entries found. Add to your schedule to display here.", "ticketing.form-step.event-type": "Select Event", "ticketing.form-step.form-type": "Select a Type", "ticketing.form-step.event-information": "Add Event Details", "ticketing.form-step.pass-details": "Add Pass Details", "ticketing.form-step.events-by-team": "Add Team", "ticketing.form-step.events-by-teams": "Add Teams", "ticketing.form-step.ticket-pricing": "Add Tickets", "ticketing.form-step.pass-seating": "Add Seating", "ticketing.form-step.review": "Review & Publish", "ticketing.form-step.header": "Step {stepNumber}: {stepName}", "ticketing.form-step.header.event-type": "Event Type", "ticketing.form-step.header.ticketed-event-type": "Ticketed Event Type", "ticketing.form-step.header.pass-type": "Pass Type", "ticketing.form-step.header.event-selection": "Select What Event To Create", "ticketing.form-step.header.setup-pass-pricing": "Seating Information", "ticketing.form-step.header.event-information": "Event Details", "ticketing.form-step.header.pass-details": "Pass Details", "ticketing.form-step.header.team-and-event-information": "Team Information", "ticketing.form-step.header.setup-ticket-pricing": "Ticket Information", "ticketing.form-step.header.review-information": "Review & Publish", "ticketing.form-step.form-type.subheader": "Select whether {title} will have general admission or reserved seating.", "ticketing.form-step.event-type.general-admission-header": "General Admission", "ticketing.form-step.event-type.general-admission-description": "All tickets sold for this event will be general admission — fans can sit anywhere. ", "ticketing.form-step.event-type.general-admission-description-tickets": "All tickets sold for this event will be general admission — fans can sit anywhere. ", "ticketing.form-step.event-type.general-admission-description-passes": "All passes sold for this event will be general admission — fans can sit anywhere. ", "ticketing.form-step.event-type.reserved-seating-header": "Reserved Seating", "ticketing.form-step.event-type.reserved-seating-description": "All tickets sold for this event will be based on the venue and its available seats. To set up a new venue, {contactSupport}", "ticketing.form-step.event-type.cannot-change-type-warning": "Event type cannot be changed since this event has been published.", "ticketing.form-step.event-type.no-venues-set-up": "You have no venues set up yet. Contact support to set up your venues.", "ticketing.form-step.event-type.no-seating-charts-set-up": "No seating layout(s) set up for your venue(s). Please contact support to set up a new seating layout.", "ticketing.form-step.event-type.no-seating-charts-set-up-singular": "No seating layout(s) set up for this venue. Please contact support to set up a new seating layout.", "ticketing.form-step.event-selection.custom-event": "Custom Event", "ticketing.form-step.event-selection.custom-event-description": "Create an event for an activity not tied to a schedule entry in Hudl (e.g., prom, fundraiser). Cannot be used with passes.", "ticketing.form-step.event-selection.scheduled-event": "Scheduled Event", "ticketing.form-step.header.event-selection.review-header": "Event Created", "ticketing.form-step.header.event-selection.review-cta": "Edit Event", "ticketing.form-step.event-selection.scheduled-event-description": "Create a team sports event tied to an existing schedule entry in Hudl (e.g., single game).", "ticketing.venue-selection.error-fetching-venues": "There was a problem loading your venues and seating layouts. Please try again.", "ticketing.create-pass-page.cannot-change-team-warning": "Team cannot be edited or removed once a reserved seating pass has been published.", "ticketing.create-pass-page.cannot-change-teams-warning": "Teams cannot be edited or removed once a general admission pass has been published.", "ticketing.create-pass-page.reserved-seating.venue-selection-description": "Select a venue and seating layout for this pass. If you need to make changes to a venue or its seating layout, please {contactSupport}.", "ticketing.create-pass-page.reserved-seating-description": "All passes sold for this event will be based on the venue, team, and its available seats. To set up a new venue, {contactSupport}", "ticketing.create-pass-page.cannot-change-type-warning": "Pass type cannot be changed since this pass has been published.", "ticketing.create-pass-page.save-as-draft-toast.error-saving-as-draft": "There was an error saving your pass as a draft.", "ticketing.create-pass-page.create-pass": "Create Pass", "ticketing.create-pass-page.review-pass": "Review Pass", "ticketing.create-pass-page.pass-information-header": "Pass Information", "ticketing.create-pass-page.pass-information": "Give your fans some more details about the pass. They'll be able to see this information on the event ticketing page.", "ticketing.create-pass-page.pass-name": "Pass Name", "ticketing.create-pass-page.pass-name-instruction": "Enter pass name here.", "ticketing.create-pass-page.pass-name-character-limit": "Character limit: {nameCharactersUsed}/{formTitleMaxLength}", "ticketing.create-pass-page.pass-description": "Pass Description", "ticketing.create-pass-page.pass-description-instruction": "Describe what this pass is for.", "ticketing.create-pass-page.pass-description-character-limit": "Character limit: {descriptionCharactersUsed}/{formDescriptionMaxLength}", "ticketing.create-pass-page.pass-character-limit-error": "Character limit exceeded.", "ticketing.create-pass-page.start-date-label": "Start Date", "ticketing.create-pass-page.end-date-label": "End Date", "ticketing.create-pass-page.pass-access-details-label": "Pass Access Details", "ticketing.create-pass-page.pass-access-details-description": "Select the team(s) that the pass is valid for.", "ticketing.create-pass-page.pass-access-details-description-single-team": "Select the team the pass is valid for.", "ticketing.create-pass-page.team-selector-label": "Team", "ticketing.create-pass-page.teams-selector-label": "Teams", "ticketing.create-pass-page.team-selector-placeholder-ga": "Select teams", "ticketing.create-pass-page.team-selector-placeholder-rs": "Select team", "ticketing.create-pass-page.pass-price-details-label": "Pass Pricing", "ticketing.create-pass-page.pass-price-details-description": "Enter the pricing options fans can purchase for this event. Make sure to fill out all required (*) fields to publish or save as draft.", "ticketing.create-pass-page.pass-price-label": "Price", "ticketing.create-pass-page.pass-disclaimer": "Make sure the information entered is correct. Once you publish, passes will become available to fans.", "ticketing.create-pass-page.success-publishing-pass-config": "{passConfigName} was published successfully.", "ticketing.create-pass-page.error-publishing-pass-config": "There was an error publishing your pass.", "ticketing.create-pass-page.team-selector-empty": "All teams have been selected", "ticketing.create-pass-page.end-date-error": "End date must be after start date.", "ticketing.create-pass-page.success-saving-draft-pass-config": "{passConfigName} was successfully saved as a draft.", "ticketing.create-pass-page.pass-quantity-label": "Quantity for Sale", "ticketing.create-pass-page.pass-quantity-placeholder": "Enter quantity", "ticketing.create-pass-page.pass-quantity-error": "Quantity must be greater than 0.", "ticketing.create-pass-page.quantity-help-text": "If no quantity is set, it will be unlimited.", "ticketing.create-pass-page.quantity-less-than-existing": "Quantity is less than the original amount. This pass may become sold out.", "ticketing.create-pass-page.review.pass-name": "Pass Name", "ticketing.create-pass-page.review.active-time-period": "Active Time Period", "ticketing.create-pass-page.review.pass-description": "Pass Description", "ticketing.create-pass-page.review.header.teams-included": "Teams Included", "ticketing.create-pass-page.review.header.single-team-included": "Team Included", "ticketing.create-pass-page.review.header.pass-details": "Pass Details", "ticketing.create-pass-page.review.header.seating-information": "Seating Details", "ticketing.create-pass-page.review.button.edit-details": "Edit Details", "ticketing.create-pass-page.review.button.edit-teams": "Edit Teams", "ticketing.create-pass-page.review.button.edit-single-team": "Edit Team", "ticketing.create-pass-page.review.button.edit-seating": "Edit Seating", "ticketing.create-pass-page.pass-name-error-help-text": "Pass name is required.", "ticketing.scanner-access-code-modal.header": "Ticket Scanning Access", "ticketing.scanner-access-code-modal.instructions": "Event staff will need this code to scan tickets in the Hudl Ticket Reader app. Once they enter the code, they can scan tickets for any ticketed event you’ve created.", "ticketing.scanner-access-code-modal.access-code-title": "Access Code", "ticketing.scanner-access-code-modal.copy-code-button": "Copy Code", "ticketing.scanner-access-code-modal.error-text": "There was an error generating the access code. Please try again.", "ticketing.home-page.copy-access-code.code-copied": "Access Code Copied to Clipboard", "ticketing.home-page.copy-access-code.code-copy-failed": "Could not copy access code to clipboard. Try manually copying the access code.", "ticketing.pass-details-page.pass-information": "Pass Information", "ticketing.pass-details-page.active-time-period": "Active Time Period", "ticketing.pass-details-page.pass-description": "Pass Description", "ticketing.pass-details-page.pass-pricing": "Pass Pricing", "ticketing.pass-details-page.pricing": "Pricing", "ticketing.pass-details-page.quantity": "Quantity for Sale", "ticketing.pass-details-page.error-loading-pass-details": "Error loading pass details.", "ticketing.pass-details-page.pass-events-header-name": "Event", "ticketing.pass-details-page.pass-events-header-date-time": "Date & Time", "ticketing.pass-details-page.pass-events-header-gender-level-sport": "Gender, Sport", "ticketing.pass-details-page.events-headline": "Pass Access to these Events ({eventsCount})", "ticketing.pass-details-page.events-empty-state": "No events found for this pass.", "ticketing.pass-sections-selector.already-published-note": "Published passes with reserved seating are not editable or removable. You may only add new sections by selecting existing sections below.", "ticketing.pass-sections-selector.select-all-sections": "Add All Sections", "ticketing.pass-sections-selector.sections-available": "Sections Available", "ticketing.pass-sections-selector.sections-available-description": "Select the sections available to pass holders when they select seats during purchase.", "ticketing.pass-sections-selector.add-sections-for-more-options": "No matching results.", "ticketing.pass-sections-selector.sections-selector-label": "Sections", "ticketing.pass-sections-selector.section-selector-empty": "All sections have been selected", "ticketing.pass-sections-selector.sections-selector-instruction": "Select sections", "ticketing.pass-sections-selector.published-seating-sections": "Published sections: {publishedSections}", "ticketing.ticket-type-modal.ticket-type-name": "Ticket Type Name", "ticketing.ticket-type-modal.ticket-type-name-character-limit": "Character limit: {charactersUsed}/{ticketTypeNameMaxLength}", "ticketing.ticketed-event-details.event-info.header": "Event Info", "ticketing.ticketed-event-details.event-info.date": "Date & Time:", "ticketing.ticketed-event-details.event-info.description-header": "Event Description:", "ticketing.ticketed-event-details.last-updated-text": "Last Updated:", "ticketing.ticketed-event-details.error-fetching-data-for-edit-function": "Could not fetch data to enable editing from this page.", "ticketing.ticketed-event-details.ticketing-summary.header": "Ticketing Summary", "ticketing.ticketed-event-details.ticketing-summary.info-text": "See how your event is performing in ticketing sales, revenue, and attendance.", "ticketing.ticketed-event-details.ticketing-summary.statistics.tickets-sold": "Tickets Sold", "ticketing.ticketed-event-details.ticketing-summary.statistics.tickets-scanned": "Tickets Scanned", "ticketing.ticketed-event-details.ticketing-summary.statistics.estimated-revenue": "Estimated Revenue", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.ticket-type": "Ticket Type", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.price-per-ticket": "Price Per Ticket", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.tickets-sold": "Tickets Sold", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.comp-tickets": "Comp Tickets", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.quantity-available": "Quantity Available", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.revenue": "Revenue", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.digital-sales": "Digital Sales", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.cash-sales": "Cash Sales", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.footer.total-revenue": "Total Revenue", "ticketing.ticketed-event-details.ticketing-summary.attendance.header": "Attendance Details", "ticketing.ticketed-event-details.ticketing-summary.attendance-table.header.ticket-type": "Ticket Type", "ticketing.ticketed-event-details.ticketing-summary.attendance-table.header.tickets-scanned": "Tickets Scanned", "ticketing.ticketed-event-details.ticketing-summary.attendance-table.header.tickets-sold": "Tickets Sold", "ticketing.ticketed-event-details.ticketing-summary.attendance-table.rows.pass-holder": "Pass Holder", "ticketing.ticketed-event-details.ticketing-summary.attendance-table.footer.total-tickets-scanned": "Total Tickets Scanned", "ticketing.ticketed-event-details.ticketing-summary.revenue-table.no-limit": "Unlimited", "ticketing.ticketed-event-details.ticketing-summary.estimated-revenue.header": "Estimated Revenue Details", "ticketing.ticketed-event-details.ticketing-summary.estimated-revenue.info-text": "Estimated revenue only reflects current ticket prices—it doesn't account for any tickets sold at a previous price. For exact revenue reporting, visit your {stripeDashboardLink}", "ticketing.ticketed-event-details.ticketing-summary.estimated-revenue.stripe-dashboard-link": "Stripe Dashboard.", "ticketing.ticketed-event-details.error.note": "We're having trouble loading this content. Try reloading the page.", "ticketing.purchaser-list.empty-state-headline": "No {itemType} Sold Yet", "ticketing.purchaser-list.empty-state-text": "The list will automatically update when {itemType} have been purchased by fans.", "ticketing.purchaser-list.empty-state-headline-passes": "No Passes Sold", "ticketing.purchaser-list.empty-state-text-passes": "The list will automatically update when passes have been purchased by fans.", "ticketing.purchaser-list.empty-state-headline-tickets": "No Tickets Sold", "ticketing.purchaser-list.empty-state-text-tickets": "The list will automatically update when tickets have been purchased by fans.", "ticketing.purchaser-list.empty-state-headline-comp-tickets": "No Complimentary Tickets Sent", "ticketing.purchaser-list.empty-state-text-comp-tickets": "Let's get fans to the game! Send complimentary tickets by clicking “Send Comp Tickets” above.", "ticketing.purchaser-list.empty-state-headline-comp-passes": "No Complimentary Passes Sent", "ticketing.purchaser-list.empty-state-text-comp-passes": "Let's get fans to the game! Send complimentary passes by clicking “Send Comp Passes” above.", "ticketing.purchaser-list.point-of-sale-help-text": "Some information may not be available for {itemType} purchases made at the event.", "ticketing.purchaser-list.reserved-seating-header": "Reserved Seating", "ticketing.purchaser-list.ticket-types-purchased-header": "Ticket Types", "ticketing.purchaser-list.download": "Download", "ticketing.download-purchaser-list-modal.header": "Download Purchaser List", "ticketing.download-purchaser-list-modal.subhead": "Customize information and download a list of all purchasers for this {itemType}.", "ticketing.download-purchaser-list-modal.download-button.csv": "Download (.csv)", "ticketing.download-purchaser-list-modal.download-button.pdf": "Download (.pdf)", "ticketing.download-purchaser-list-modal.include-fields-header": "Select Fields to Include:", "ticketing.download-purchaser-list-modal.options-header": "Options:", "ticketing.download-purchaser-list-modal.checkbox-label.first-name": "First Name", "ticketing.download-purchaser-list-modal.checkbox-label.last-name": "Last Name", "ticketing.download-purchaser-list-modal.checkbox-label.email": "Email", "ticketing.download-purchaser-list-modal.checkbox-label.reserved-seats": "Reserved Seats", "ticketing.download-purchaser-list-modal.checkbox-label.order-number": "Order Number", "ticketing.download-purchaser-list-modal.checkbox-label.time-of-purchase": "Time of Purchase", "ticketing.download-purchaser-list-modal.checkbox-label.amount-purchased": "Amount Purchased", "ticketing.download-purchaser-list-modal.checkbox-label.ticket-types-purchased": "Ticket Types", "ticketing.download-purchaser-list-modal.checkbox-label.purchase-source": "Purchase Source", "ticketing.download-purchaser-list-modal.checkbox-label.payment-type": "Payment Type", "ticketing.download-purchaser-list-modal.checkbox-label.options.exclude-no-name-email": "Exclude purchasers without name or email", "ticketing.download-purchaser-list-modal.error-loading-list": "There was an error fetching the purchaser list data. Please try again.", "ticketing.download-purchaser-list-modal.error-downloading-list": "There was an error downloading the purchaser list. Please try again.", "ticketing.download-purchaser-list-modal.exclude-complimentary": "Exclude complimentary recipients", "ticketing.ticketed-event-details.purchaser-name": "Purchaser Name", "ticketing.ticketed-event-details.purchaser-email": "Email", "ticketing.ticketed-event-details.purchaser-order-number": "Order #", "ticketing.ticketed-event-details.time-of-purchase": "Time of Purchase", "ticketing.ticketed-event-details.quantity-purchased": "Amount Purchased", "ticketing.ticketed-event-details.number-tickets": "# of Tickets", "ticketing.ticketed-event-details.number-passes": "# of Passes", "ticketing.ticketed-event-details.purchase-source": "Purchase Source", "ticketing.ticketed-event-details.purchaser-payment-type": "Payment Type", "ticketing.add-ticket-type-modal.error-saving-ticket-type": "There was an error saving your new ticket type.", "ticketing.add-ticket-type-modal.add-to-section": "Add to Section", "ticketing.add-ticket-type-modal.select-sections": "Select Sections", "ticketing.add-ticket-type-modal.select-help-text": "Automatically add your new ticket type to sections in the {venueConfigName} seating layout.", "ticketing.add-ticket-type-modal.add-ticket-type-to-section": "Once created, you can add this ticket type to any section in any seating layout by selecting it from the “Ticket Type(s)” dropdown.", "ticketing.ticket-types-for-category.published-ticket-types": "Published Ticket Types: {publishedTicketTypes}", "ticketing.ticket-types-for-category.ticket-types-select-label": "Select Ticket Type(s)", "ticketing.ticket-types-for-category.ticket-types-select-placeholder": "Select Ticket Types", "ticketing.ticket-types-for-category.ticket-types-by-section": "Ticket Types By Section", "ticketing.ticket-types-for-category.already-published-note": "Published ticket types for reserved seating events are not editable or removable. You may only add new ticket types by selecting existing ticket types below or creating a new ticket type to add with the button above.", "ticketing.ticket-types-for-category.create-ticket-type": "Create Ticket Type", "ticketing.ticket-types-for-category.no-sections": "No Sections Available", "ticketing.ticket-types-for-category.no-sections-contact-support": "Contact support to add sections to this seating layout.", "ticketing.ticket-types-for-category.add-ticket-type-for-more-options": "No matching results. For more options, create a new ticket type.", "ticketing.category-item.section-name": "Section Name", "ticketing.visibility.tickets-title": "Set Event Visibility", "ticketing.visibility.pass-title": "Set Pass Visibility", "ticketing.visibility.tickets-subtitle": "Public events are immediately available for fans to purchase. Private events remain hidden from fans until they are either made public or shared via a link. Setting as Not For Sale will not allow for any sales for this event.", "ticketing.visibility.pass-subtitle": "Public passes are immediately available for fans to purchase. Private passes remain hidden from fans until they are either made public or shared via a link. Setting as Not For Sale will not allow for any sales for this pass.", "ticketing.visibility.public": "Public", "ticketing.visibility.private": "Private", "ticketing.visibility.not-for-sale": "Not For Sale", "ticketing.form-fields.additional-fan-information.header": "Request Additional Fan Information (Optional)", "ticketing.form-fields.additional-fan-information.subhead": "Need some additional details for this event? Add a custom field for fans to fill out when they purchase tickets.", "ticketing.form-fields.add-form-field": "Add Custom Field", "ticketing.form-fields.add-form-field.max-form-fields-error-message": "Max limit of {maxFormFields} custom fields used.", "ticketing.form-fields.select.label": "Add Existing Custom Field", "ticketing.form-fields.select.placeholder": "Select custom field", "ticketing.form-fields.select.empty-message": "No custom fields are available. To create a new one, use the “Add Custom Field” button.", "ticketing.form-fields.item.required": "Required", "ticketing.form-fields.item.required.yes": "Yes", "ticketing.form-fields.item.required.no": "No", "ticketing.form-fields.item.help-text": "Help Text", "ticketing.form-fields.add-field-modal.header": "Add Custom Field", "ticketing.form-fields.add-field-modal.name.label": "Name", "ticketing.form-fields.add-field-modal.name.placeholder": "Field name", "ticketing.form-fields.add-field-modal.name.character-limit": "Character limit: {charactersUsed}/{fieldNameMaxLength}", "ticketing.form-fields.add-field-modal.help-text.label": "Help Text (Optional)", "ticketing.form-fields.add-field-modal.help-text.placeholder": "Add some additional details", "ticketing.form-fields.add-field-modal.help-text.character-limit": "Character limit: {charactersUsed}/{helpTextMaxLength}", "ticketing.form-fields.add-field-modal.required.label": "Is this field required for fans when purchasing?", "ticketing.form-fields.add-field-modal.required.yes": "Yes", "ticketing.form-fields.add-field-modal.required.no": "No, it's optional", "ticketing.form-fields.add-field-modal.add-button": "Add Custom Field", "ticketing.form-fields.add-field-modal.toast.error": "There was an error saving your new custom field.", "ticketing.form-fields.review.header": "Request Additional Fan Information", "ticketing.form-fields.review.required": "Required:", "ticketing.form-fields.error-fetching": "There was an error fetching custom fields. Please try again.", "ticketing.tabs.events": "Events", "ticketing.tabs.published": "Published", "ticketing.tabs.non-ticketed": "Non-Ticketed", "ticketing.tabs.draft": "Draft", "ticketing.tabs.active": "Active", "ticketing.tabs.current": "Current", "ticketing.tabs.past": "Past", "ticketing.tabs.passes": "Passes", "ticketing.tabs.overview": "Overview", "ticketing.tabs.purchaser-list": "Purchaser List", "ticketing.save-as-draft": "Save as Draft", "ticketing.review": "Review", "ticketing.publish": "Publish", "ticketing.cancel": "Cancel", "ticketing.remove": "Remove", "ticketing.continue": "Continue", "ticketing.create": "Create", "ticketing.date": "Date", "ticketing.date.at.time": "{date} at {time}", "ticketing.price": "Price", "ticketing.add-ticket-type.label": "Ticket Type", "ticketing.start-time": "Start Time", "ticketing.add-ticketing": "Add Ticketing", "ticketing.create-pass": "Create Pass", "ticketing.this-pass": "this pass", "ticketing.retry": "Retry", "ticketing.refresh": "Refresh", "ticketing.reload": "Reload", "ticketing.contact-support": "Contact Support", "ticketing.contact-support-lower": "contact support", "ticketing.save": "Save", "ticketing.save-seats": "Save Seats", "ticketing.add": "Add", "ticketing.update": "Update", "ticketing.back": "Back", "ticketing.manage": "Manage", "ticketing.select-all": "Select All", "ticketing.deselect-all": "Deselect All", "ticketing.events": "Events", "ticketing.tickets": "tickets", "ticketing.passes": "passes", "ticketing.ticket": "ticket", "ticketing.pass": "pass", "ticketing.pass.upper": "Pass", "ticketing.event": "event", "ticketing.event.upper": "Event", "ticketing.past-date-help-text": "Date cannot be in the past.", "ticketing.source.mobile": "Mobile", "ticketing.source.web": "Online", "ticketing.source.pos": "At Event", "ticketing.source.complimentary": "Complimentary", "ticketing.source.shared": "Shared", "ticketing.source.transfer": "Transfer", "ticketing.source.manual-entry": "Manual Entry", "ticketing.payment-type.electronic": "Card", "ticketing.payment-type.external": "Cash", "ticketing.unlimited": "Unlimited", "ticketing.none": "None", "ticketing.teams": "Teams", "ticketing.team": "Team", "ticketing.quantity-available": "Quantity Available", "ticketing.first-name": "First Name", "ticketing.last-name": "Last Name", "ticketing.email": "Email", "ticketing.ticket-type": "Ticket Type", "ticketing.ticket-quantity": "Quantity", "ticketing.edit": "Edit", "ticketing.submit": "Submit", "ticketing.enter-first-name": "Enter first name", "ticketing.enter-last-name": "Enter last name", "ticketing.enter-email-address": "Enter email address", "ticketing.move-to-draft": "Move to Draft", "ticketing.provide-first-name": "Please provide a first name", "ticketing.provide-last-name": "Please provide a last name", "ticketing.provide-valid-email": "Please provide a valid email address", "ticketing.complimentary.recipient-placeholder": "Recipient {index}", "ticketing.seat-selection.section": "Section {section}", "ticketing.seat-selection.row": "Row {row}", "ticketing.seat-selection.seat": "Seat {seat}", "ticketing.seat-selection.zone": "Zone {zone}", "ticketing.seat-selection.seats-selected": "Seats Selected:", "ticketing.seat-selection.selected-seats": "{seatsSelected} Seats", "ticketing.seat-selection.seats-selected-count": "{seatsSelected} Seats Selected", "ticketing.seat-selection.select-seats": "Select Seats", "ticketing.seat-selection.edit-seats": "<PERSON>", "ticketing.seat-selection.remove": "Remove", "ticketing.complimentary.reserved-seating.tickets.hold-seats.title": "Holding Seats for Comp Tickets", "ticketing.complimentary.reserved-seating.passes.hold-seats.title": "Holding Seats for Comp Passes", "ticketing.complimentary.reserved-seating.tickets.hold-seats.description": "We can only hold your seat selection for 15 minutes. If time expires before you send the tickets, you’ll need to select the seats again.", "ticketing.complimentary.reserved-seating.passes.hold-seats.description": "We can only hold your seat selection for 15 minutes. If time expires before you send the passes, you’ll need to select the seats again.", "ticketing.complimentary.reserved-seating.hold-seats.get-started": "Get Started", "ticketing.complimentary.provide-first-name": "Please provide a first name.", "ticketing.complimentary.provide-last-name": "Please provide a last name.", "ticketing.complimentary.provide-valid-email": "Please provide a valid email address.", "ticketing.complimentary.provide-valid-quantity": "Quantity must be greater than 0 and less than 25.", "ticketing.complimentary.remove": "Remove", "ticketing.complimentary.select-ticket-type": "Please select a ticket type.", "ticketing.complimentary.add-recipient": "Add Recipient", "ticketing.complimentary.totals.title": "Totals", "ticketing.complimentary.totals.recipients": "Recipients", "ticketing.complimentary.nav-button.title-tickets": "Send Comp Tickets", "ticketing.complimentary.nav-button.title-tickets.mobile": "Comp Tickets", "ticketing.complimentary.totals-tickets": "Complimentary Tickets", "ticketing.complimentary.recipients-tickets": "Complimentary Tickets Recipients", "ticketing.complimentary.confirm.send-tickets": "Send Ticket{numberOfItems, plural, =1 {} other {s}}", "ticketing.complimentary.review.header-tickets": "Review & Send", "ticketing.complimentary.review.sub-header-tickets": "Make sure all the information is correct before sending complimentary tickets.", "ticketing.complimentary.edit.header-tickets": "Send Complimentary Tickets", "ticketing.complimentary.edit.sub-header-tickets": "Provide the following information for each fan who should receive tickets free of charge. Their complimentary tickets to {eventName} will be delivered to the email provided.", "ticketing.complimentary.recipient-information-tickets": "Recipient & Ticket Information", "ticketing.complimentary.recipient-information-passes": "Recipient & Pass Information", "ticketing.complimentary.send.confirm-tickets": "{numCompTicketsSent} complimentary tickets sent", "ticketing.complimentary.send.confirm-tickets.no-count": "Complimentary tickets sent", "ticketing.complimentary.send.fail-tickets": "There was a problem sending your complimentary tickets. Please check the event's ticket availability and try again. If the issue persists, {contactSupport}.", "ticketing.complimentary.nav-button.title-passes": "Send Comp Passes", "ticketing.complimentary.nav-button.title-passes.mobile": "Comp Passes", "ticketing.complimentary.totals-passes": "Complimentary Passes", "ticketing.complimentary.recipients-passes": "Complimentary Pass Recipients", "ticketing.complimentary.confirm.send-passes": "Send Pass{numberOfItems, plural, =1 {} other {es}}", "ticketing.complimentary.review.header-passes": "Review & Send", "ticketing.complimentary.review.sub-header-passes": "Make sure all the information is correct before sending complimentary passes.", "ticketing.complimentary.edit.header-passes": "Send Complimentary Passes", "ticketing.complimentary.edit.sub-header-passes": "Provide the following information for each fan who should receive passes free of charge. Their complimentary passes for {eventName} will be delivered to the email provided.", "ticketing.complimentary.send.confirm-passes": "{numCompTicketsSent} complimentary passes sent", "ticketing.complimentary.send.fail-passes": "There was a problem sending your complimentary passes. Please check the event's ticket availability and try again. If the issue persists, {contactSupport}.", "ticketing.complimentary.send.fail.title": "An Error Occurred", "ticketing.complimentary.return-to-event-details": "Return to Event Details", "ticketing.complimentary.reserved-seating.timer.timeout": "The hold on your seats has expired. You'll need to select your seats again.", "ticketing.complimentary.reserved-seating.timer.display": "Holding Seats For", "ticketing.complimentary.reserved-seating.timer.okay": "Okay", "ticketing.complimentary.reserved-seating.click-to-enable-chart": "Please click “Select Seats” to activate the map", "ticketing.complimentary.reserved-seating.modal.title": "Select General Admission Seats", "ticketing.complimentary.reserved-seating.modal.subtitle": "Choose the number of General Admission seats to reserve:", "ticketing.complimentary.csv.import-button": "Import Recipients", "ticketing.complimentary.csv.modal.header": "Import Recipients", "ticketing.complimentary.csv.modal.subtitle": "Download the template below and fill in the required information to import recipients.", "ticketing.complimentary.csv.modal.import-button": "Import", "ticketing.complimentary.csv.modal.error.wrong-file-type": "Only .csv files are supported.", "ticketing.complimentary.csv.modal.error.no-rows": "No data found in the .csv file. Please check the file and try again.", "ticketing.complimentary.csv.modal.error.invalid-file": "Invalid file. Please check the file and try again.", "ticketing.complimentary.csv.modal.download-instruction": "1. Download template below:", "ticketing.complimentary.csv.modal.upload-instruction": "2. Upload template with filled out information:", "ticketing.complimentary.csv.modal.download-template-button": "Download Template", "ticketing.complimentary.csv.modal.upload-file-button": "Upload File", "ticketing.complimentary.csv.modal.review.filename": "File Name:", "ticketing.complimentary.csv.modal.review.recipient-count": "Recipient Count:", "ticketing.complimentary.csv.modal.review.ticket-type-heading": "Select Ticket Type (Optional)", "ticketing.complimentary.csv.modal.review.ticket-type-sub": "This ticket type will be applied to all recipients. Individual recipient's ticket type can be edited after import.", "ticketing.fee-strategy.example-text.ticket": "Ex. ${ticketPrice} ticket, {boldText1} and {boldText2}", "ticketing.fee-strategy.example-text.pass": "Ex. ${ticketPrice} pass, {boldText1} and {boldText2}", "ticketing.fee-strategy.example-text.bold-1": "fans would pay ${fansPay}", "ticketing.fee-strategy.example-text.bold-2": "you would receive ${youReceive}", "ticketing.fee-strategy.who-covers-fees": "Who do you want to cover the {feesForEventLink}?", "ticketing.fee-strategy.who-covers-fees-link-event": "fees for this event", "ticketing.fee-strategy.who-covers-fees-link-pass": "fees for this pass", "ticketing.fee-strategy.who-covers-fees-link-purchases": "fees associated with ticket purchases", "ticketing.fee-strategy.ticket-price-minimum": "Set price to a minimum of $2 if your organization is covering fees.", "ticketing.fee-strategy.ticket-price-minimum-RS": "Ticket prices must be a minimum of $2 if your organization is covering fees.", "ticketing.fee-strategy.pass-price-minimum": "Set price to a minimum of $4 if your organization is covering fees.", "ticketing.fee-strategy.fees-covered-by": "Fees Covered By", "ticketing.fee-strategy.fees-covered-by.your-fans": "Your Fans", "ticketing.fee-strategy.fees-covered-by.your-organization": "Your Organization", "ticketing.fee-strategy.applied-at-checkout": "Fees will automatically be applied for fans at checkout.", "ticketing.fee-strategy.deducted-from-revenue": "You don't pay any extra - fees will automatically be deducted from your total revenue.", "ticketing.visibility": "Visibility", "ticketing.status": "Status", "ticketing.my-organizations.error-fetching": "There was an error fetching your organizations. Please try again.", "ticketing.not-org-admin-of-current-org": "You are not authorized as an administrator for this organization.", "ticketing.org-ticketing-enabled.error-fetching": "There was an error fetching your account information. Please try again.", "ticketing.org-ticketing-enabled.not-enabled": "Ticketing is not enabled for this organization.", "ticketing.publish-events.publishing-disabled-by-payment-status": "Events cannot be published until payment setup and onboarding is complete.", "ticketing.publish-passes.publishing-disabled-by-payment-status": "Passes cannot be published until payment setup and onboarding is complete.", "ticketing.schedule-reference-uploader.error.loading-teams": "There was an error fetching your teams. Please try again.", "ticketing.schedule-reference-uploader.header": "Add Your Schedule", "ticketing.schedule-reference-uploader.subheading": "Add events by importing a file and we’ll create these events for you with the information provided. Event set up will begin once onboarding is complete. If you need to expedite your event creation, {contactSupport}.", "ticketing.schedule-reference-uploader.instructions.info-to-include": "Helpful Information to Include in Schedule:", "ticketing.schedule-reference-uploader.instructions.team": "• Team", "ticketing.schedule-reference-uploader.instructions.date": "• Date (MM/DD/YYYY)", "ticketing.schedule-reference-uploader.instructions.start-time": "• Start Time", "ticketing.schedule-reference-uploader.instructions.opponent": "• Opponent", "ticketing.schedule-reference-uploader.instructions.location": "• Location (Home vs Away)", "ticketing.schedule-reference-uploader.instructions.recommended-file-name": "• Recommended file name structure: Season + Sport (e.g. 2024-2025 Basketball)", "ticketing.schedule-reference-uploader.drag-drop.drop-here-or": "Drop files here or %{browse}", "ticketing.schedule-reference-uploader.drag-drop.browse": "browse folder", "ticketing.schedule-reference-uploader.drag-drop.upload-error.file-type": "File type not supported. Please upload a file with a valid type.", "ticketing.schedule-reference-uploader.drag-drop.upload-error.file-size": "Maximum file size exceeded. Please upload a file that is less than {maxFileSize} MB.", "ticketing.schedule-reference-uploader.drag-drop.upload-error.file-count": "A maximum of {maxFileCount} files can be uploaded at a time.", "ticketing.schedule-reference-uploader.drag-drop.upload-error.duplicate": "This file has already been added.", "ticketing.schedule-reference-uploader.drag-drop.upload-error.unsupported": "This file is not supported.", "ticketing.schedule-reference-uploader.drag-drop.instruction-note": "Supported file types: {fileTypes}", "ticketing.schedule-reference-uploader.file-list.header": "{numberFiles} file{numberFiles, plural, =1 {} other {s}} imported:", "ticketing.schedule-reference-uploader.team-select.label": "Select Team(s)", "ticketing.schedule-reference-uploader.team-select.placeholder": "Select Team(s) for this schedule", "ticketing.schedule-reference-uploader.notes.label": "Additional Notes", "ticketing.schedule-reference-uploader.notes.placeholder": "Enter any additional details that will help with creating events based off your schedule", "ticketing.schedule-reference-uploader.notes.character-limit": "Character limit: {charactersUsed}/{notesMaxLength}", "ticketing.schedule-reference-uploader.error-toast.content": "There was an error uploading your schedule files. Please try again.", "ticketing.upload": "Upload", "ticketing.default-ticket-types.headline": "Add Ticket Types", "ticketing.default-ticket-types.subhead": "Create the standard ticket type options you plan to include for most events. You can always add more ticket types later.", "ticketing.default-ticket-types.ticket-types": "Ticket Types", "ticketing.default-ticket-types.error-state": "There was an error fetching your default ticket types. Please try again.", "ticketing.team-ticket-type-input.ticket-name": "Ticket Name", "ticketing.team-ticket-type-input.ticket-name-placeholder": "Enter ticket name", "ticketing.team-ticket-type-input.teams": "Teams", "ticketing.team-ticket-type-input.teams-placeholder": "Select teams", "ticketing.team-ticket-type-input.add-ticket-type": "Add Ticket Type", "ticketing.duplicate": "Duplicate", "ticketing.team-ticket-type-input.team-tooltip": "This ticket type will be included by default for any selected teams' ticketed events.", "ticketing.payout-selection.header": "Payment Details", "ticketing.payout-selection.subheader": "Choose how your ticketing revenue gets paid out to you. Some payout methods may require additional information.", "ticketing.payout-selection.error-loading-page": "Something went wrong and we had trouble loading the page. Please refresh and try again.", "ticketing.payout-selection.direct-deposits.label": "Direct Deposit (Recommended)", "ticketing.payout-selection.direct-deposits.description": "Get paid weekly via a secure deposit directly into your bank account using Stripe. You'll also get access to revenue tracking and analytics.", "ticketing.payout-selection.direct-deposits.incomplete.header": "Hudl uses Stripe to support direct deposits.", "ticketing.payout-selection.direct-deposits.incomplete.message": "Complete the Stripe onboarding steps to finish setting up this payment method. When onboarding is complete, refresh the page to see your updated status.", "ticketing.payout-selection.direct-deposits.incomplete.footer": "Current status: Incomplete.", "ticketing.payout-selection.direct-deposits.complete.header": "You're all set!", "ticketing.payout-selection.direct-deposits.needs-review.header": "Additional information needed by <PERSON><PERSON>.", "ticketing.payout-selection.direct-deposits.needs-review.message": "<PERSON>e is requesting for additional information to continue reviewing your account. Go to <PERSON>e to complete request.", "ticketing.payout-selection.direct-deposits.complete.message": "Looks like you're already set up with <PERSON><PERSON> for direct deposit payments. Go to Stripe to see your account or click “Save” to continue your onboarding.", "ticketing.payout-selection.direct-deposits.in-review.header": "Your direct deposit information is under review by Stripe.", "ticketing.payout-selection.direct-deposits.in-review.message": "You'll receive an email once the review is complete—it may take up to 24 hours. For questions about the status of your direct deposit account, contact <PERSON><PERSON> directly.", "ticketing.payout-selection.direct-deposits.go-to-stripe": "Go to Stripe", "ticketing.payout-selection.check.label": "Physical Check", "ticketing.payout-selection.check.description": "Get paid once a month via a physical check mailed to you.", "ticketing.payout-selection.check.payee-information": "This will be the information on the Tipalti account setup. Edit account holder information if it is incorrect.", "ticketing.payout-selection.check.getting-setup-in-tipalti-header": "We're in the process of setting up your Tipalti account.", "ticketing.payout-selection.check.getting-setup-in-tipalti": "You've already submitted your information for your Tipalti account and it's currently being set up.", "ticketing.payout-selection.check.setup-in-tipalti-header": "<PERSON><PERSON><PERSON><PERSON> Account Found.", "ticketing.payout-selection.check.setup-in-tipalti": "Looks like you're already set up with <PERSON><PERSON><PERSON><PERSON> with {payeeEmail} to receive physical check payouts. If you need to update the account information, please contact {revShareEmail} for assistance.", "ticketing.payout-selection.check.rev-share-email": "<EMAIL>", "ticketing.onboarding": "Onboarding", "ticketing.onboarding.step.ticketing-settings": "Ticketing Settings", "ticketing.onboarding.step.ticketing-settings.description": "Set up your default ticketing settings. You can always adjust these settings later.", "ticketing.onboarding.step.ticketing-settings.cta": "Review Settings", "ticketing.onboarding.step.payment-details": "Set Up Payment Details", "ticketing.onboarding.step.payment-details.description": "Choose how your ticketing revenue gets paid out. Some payment methods may require additional information.", "ticketing.onboarding.step.payment-details.cta": "Set Up Payments", "ticketing.onboarding.step.payment-details.info-text.direct-deposit": "Method: Direct Deposit via Stripe", "ticketing.onboarding.step.payment-details.info-text.direct-deposit-review": "Method: Direct Deposit Under Review by <PERSON><PERSON>", "ticketing.onboarding.step.payment-details.info-text.direct-deposit-info-needed": "Method: Direct Deposit - Additional Info Needed", "ticketing.onboarding.step.payment-details.info-text.check": "Method: Physical Check", "ticketing.onboarding.step.schedule-upload": "Add Your Schedule", "ticketing.onboarding.step.schedule-upload.description": "Add events from your Hudl schedule or import a file. You'll be able to publish tickets for any of these events.", "ticketing.onboarding.step.schedule-upload.cta": "Add Schedule", "ticketing.onboarding.step.ticket-types": "Add Ticket Types", "ticketing.onboarding.step.ticket-types.description": "Create the ticket type options you plan to include for most events. You can always add more types later.", "ticketing.onboarding.step.ticket-types.cta": "Add Ticket Types", "ticketing.onboarding.step.ticket-types.info-text": "{numTicketTypes} ticket type{numTicketTypes, plural, =1 {} other {s}} saved.", "ticketing.onboarding.request-users": "Need help? Give additional users admin permissions to help you set up and manage your tickets and events.", "ticketing.invite": "Invite", "ticketing.onboarding.complete-onboarding": "Complete Onboarding", "ticketing.onboarding.header": "Get Started with Hudl Tickets", "ticketing.onboarding.subheader": "Complete the steps to publish your first event with Hudl Tickets.", "ticketing.report-cash-sales.title": "Report Cash Sales", "ticketing.report-cash-sales.helper-text": "To update the revenue details for this event with additional cash sales, please complete the form below. Your cash sales will be automatically calculated by multiplying the number of tickets sold by the ticket price.", "ticketing.report-cash-sales.ticket-type": "Ticket Type", "ticketing.report-cash-sales.ticketsSold": "Tickets Sold", "ticketing.report-cash-sales.ticketPrice": "Ticket Price", "ticketing.report-cash-sales.cashSales": "Cash Sales", "ticketing.report-cash-sales.update": "Update", "ticketing.report-cash-sales.totalCashSales": "Total Cash Sales: {total}", "ticketing.report-cash-sales.error": "There was an error reporting your cash sales. Please try again.", "ticketing.settings": "Settings", "ticketing.settings.header": "Ticketing Settings", "ticketing.settings.subheader": "Set your default settings that will help with your ticketing set up and pay out. You can adjust these at any time.", "ticketing.settings.payout-selection.direct-deposit.header": "Want to add direct deposit as a payment method?", "ticketing.settings.payout-selection.direct-deposit.under-review": "Your direct deposit information is under review by Stripe.", "ticketing.settings.payout-selection.direct-deposit.under-review.message": "You'll receive an email once the review is complete—it may take up to 24 hours. Once review is complete, direct deposit will be enabled as another payment method option. For questions about the status of your direct deposit account, contact Stripe directly.", "ticketing.settings.payout-selection.header": "Default Payout Settings", "ticketing.settings.payout-selection.check.help-text": "Submit information for the Tipalti account setup to add physical check as a payment method. If you already submitted your information, we’re in the process of setting up your account and check payout will be enabled soon.", "ticketing.settings.payout-selection.check.submitted": "Information submitted. Physical Check will be enabled as a payment method option once your account setup is complete.", "ticketing.badge.public": "Public", "ticketing.badge.private": "Private", "ticketing.badge.league": "League", "ticketing.badge.not-for-sale": "Not For Sale", "ticketing.badge.renewal": "Renewal", "ticketing.badge.unknown": "Unknown", "ticketing.event-details-change-modal.header": "Event Details Changed", "ticketing.pass-details-change-modal.header": "Pass Details Changed", "ticketing.pass-details-change-modal.warnings.dates-changed": "You're making changes to the active time period of this pass. This can impact what events are included in the pass. Would you like to continue updating the pass?", "ticketing.pass-details-change-modal.warnings.teams-changed": "You're making changes to the teams on this pass. This can impact what events are included in the pass. Would you like to continue updating the pass?", "ticketing.pass-details-change-modal.warnings.teams-changed-and-dates-changed": "You're making changes to the active time period & teams on this pass. This can impact what events are included in the pass. Would you like to continue updating the pass?", "ticketing.event-time-change-modal.details": "You're making changes to the event date/time for this event. We will automatically notify ticket purchasers of these changes. Would you like to continue updating the event?", "ticketing.pass-details-change-modal.warnings.generic": "You're making changes to this pass. This can impact what events are included in the pass. Would you like to continue with updating the pass?", "ticketing.event-time-change-modal.action.update": "Yes, Update"}}